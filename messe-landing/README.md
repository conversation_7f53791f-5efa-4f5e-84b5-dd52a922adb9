# Messe.ae Landing Page

A modern, responsive React landing page for Messe.ae - Exhibition Stands Design & Production in UAE. Built with React, TypeScript, Vite, and Tailwind CSS v4 based on the provided Figma design.

## 🚀 Features

- **Modern Tech Stack**: React 19, TypeScript, Vite, Tailwind CSS v4
- **Multi-Page Application**: Home, About Us, and Projects pages with React Router
- **Responsive Design**: Mobile-first approach with seamless desktop experience
- **Component-Based Architecture**: Modular, reusable components
- **Accessibility**: Semantic HTML with proper ARIA attributes
- **Performance Optimized**: Fast loading with Vite's hot module replacement
- **Type Safety**: Full TypeScript support
- **Testing**: Comprehensive test suite with Vitest and Testing Library

## 🎨 Tailwind CSS v4 Integration

This project uses the latest **Tailwind CSS v4** with the new CSS-first configuration approach:

### Key Changes from v3:
- ✅ **No more `tailwind.config.js`** - Configuration is now CSS-based
- ✅ **`@tailwindcss/vite` plugin** instead of PostCSS setup
- ✅ **`@theme` directive** for custom design tokens
- ✅ **Simplified setup** with better performance

### Configuration:
```css
/* src/index.css */
@import "tailwindcss";

@theme {
  --color-primary: #656CAF;
  --color-background: #E9E9E9;
  --color-gray-custom: #D9D9D9;
  --color-gray-text: #6F6F6F;
  
  --font-family-inter: 'Inter', sans-serif;
  --max-width-container: 1360px;
  --border-radius-custom: 8px;
}
```

### Vite Configuration:
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [react(), tailwindcss()],
})
```

## 📋 Pages & Sections

### Home Page (`/`)
- **Header**: Navigation with logo, menu items, and contact information
- **Hero Section**: Video placeholder with call-to-action button
- **Clients Section**: Grid of client logos and testimonials
- **Collaboration Process**: Process visualization section
- **Projects Section**: Portfolio showcase with project cards
- **Advantages Section**: "Why Choose Us?" with feature highlights
- **Contact Form**: Lead generation form with validation
- **Footer**: Company information and social links

### About Us Page (`/about`)
- **About Us Hero**: Company introduction and mission statement
- **Our Story**: Company history and background
- **Our Services**: 6 service cards with detailed descriptions
- **Expoglobal Group Timeline**: Company milestones and achievements
- **Contact Form**: Lead generation form with validation
- **Footer**: Company information and social links

### Projects Page (`/projects`)
- **Projects Gallery**: Grid of 9 project cards with images, titles, clients, and areas
- **Industries Section**: 8 industry categories with icons
- **Contact Form**: Lead generation form with validation
- **Footer**: Company information and social links

## 🛠️ Installation & Setup

1. **Navigate to the project**:
   ```bash
   cd messe-landing
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npx vite
   ```

4. **Open in browser**:
   Navigate to `http://localhost:5173` (or the port shown in terminal)

## 📝 Available Scripts

- `npx vite` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run test suite
- `npm run lint` - Run ESLint

## 🎨 Design System

### Colors
- **Primary**: `#656CAF` (Purple)
- **Background**: `#E9E9E9` (Light Gray)
- **White**: `#FFFFFF`
- **Black**: `#000000`
- **Gray Custom**: `#D9D9D9`
- **Gray Text**: `#6F6F6F`

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 400 (Regular), 500 (Medium), 700 (Bold), 800 (Extra Bold)
- **Sizes**: Responsive scaling from 16px to 64px

### Layout
- **Max Width**: 1360px container
- **Border Radius**: 8px standard
- **Spacing**: Consistent 20px/40px grid system

## 🧪 Testing

The project includes comprehensive tests covering:
- Component rendering
- User interactions
- Form functionality
- Accessibility compliance

Run tests with:
```bash
npm test
```

## 📱 Responsive Breakpoints

- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

## 🔧 Technical Decisions

- **Vite**: Chosen for fast development and build times
- **Tailwind CSS v4**: Latest version with CSS-first configuration
- **TypeScript**: Type safety and better developer experience
- **Component Architecture**: Modular design for maintainability
- **Semantic HTML**: Accessibility and SEO optimization

## 📦 Project Structure

```
src/
├── components/          # React components
│   ├── Header.tsx
│   ├── HeroSection.tsx
│   ├── ClientsSection.tsx
│   ├── CollaborationSection.tsx
│   ├── ProjectsSection.tsx
│   ├── AdvantagesSection.tsx
│   ├── ContactForm.tsx
│   └── Footer.tsx
├── pages/              # Page components
│   ├── Home.tsx
│   ├── AboutUs.tsx
│   └── Projects.tsx
├── test/               # Test files
│   ├── App.test.tsx
│   ├── AboutUs.test.tsx
│   ├── Projects.test.tsx
│   ├── setup.ts
│   └── vitest.d.ts
├── App.tsx            # Main application with routing
├── main.tsx           # Application entry point
└── index.css          # Global styles and Tailwind v4 config
```

## 🌟 Key Features Implemented

- ✅ Pixel-perfect design implementation from Figma
- ✅ Multi-page application with React Router
- ✅ Fully responsive layout
- ✅ Interactive contact form with validation
- ✅ Smooth hover effects and transitions
- ✅ Accessible navigation and form controls
- ✅ SEO-optimized semantic markup
- ✅ Performance-optimized with Tailwind CSS v4
- ✅ Cross-browser compatibility
- ✅ Clean, maintainable code structure

## 🚀 Deployment

Build the project for production:

```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

## 🔄 Migration Notes (Tailwind v3 → v4)

If migrating from Tailwind CSS v3:

1. **Remove old dependencies**:
   ```bash
   npm uninstall @tailwindcss/postcss postcss autoprefixer
   ```

2. **Install Tailwind v4**:
   ```bash
   npm install -D @tailwindcss/vite
   ```

3. **Update Vite config** to use `@tailwindcss/vite` plugin
4. **Remove** `tailwind.config.js` and `postcss.config.js`
5. **Update CSS** to use `@import "tailwindcss"` and `@theme` directive
6. **Replace custom classes** with CSS variables or arbitrary values

## 📄 License

This project is created for Messe.ae exhibition stand design company.
