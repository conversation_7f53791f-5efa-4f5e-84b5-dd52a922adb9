import type { TSESTree } from '@typescript-eslint/utils';
import type { RuleContext } from '@typescript-eslint/utils/ts-eslint';
/**
 * @return `true` if the function or method node has overload signatures.
 */
export declare function hasOverloadSignatures(node: TSESTree.FunctionDeclaration | TSESTree.MethodDefinition, context: RuleContext<string, unknown[]>): boolean;
//# sourceMappingURL=hasOverloadSignatures.d.ts.map