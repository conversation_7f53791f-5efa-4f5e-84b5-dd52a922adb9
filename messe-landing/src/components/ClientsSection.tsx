type ClientsSectionProps = {
  className?: string;
};

export default function ClientsSection({ className = '' }: ClientsSectionProps) {
  // Mock client logos
  const clients = Array(20).fill(null).map((_, index) => ({
    id: index + 1,
    name: `Client ${index + 1}`,
  }));

  return (
    <section className={`py-16 bg-background ${className}`}>
      <div className="max-w-container mx-auto px-4">
        {/* Section Title */}
        <h2 className="text-4xl font-bold text-black text-center mb-12">
          Our Projects & Clients
        </h2>

        {/* Clients Grid - First Row */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-7 mb-7">
          {clients.slice(0, 10).map((client) => (
            <div
              key={client.id}
              className="bg-white rounded-custom p-6 flex items-center justify-center h-20 shadow-sm hover:shadow-md transition-shadow"
            >
              <span className="text-2xl font-bold text-black">logo</span>
            </div>
          ))}
        </div>

        {/* Clients Grid - Second Row */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-7">
          {clients.slice(10, 20).map((client) => (
            <div
              key={client.id}
              className="bg-white rounded-custom p-6 flex items-center justify-center h-20 shadow-sm hover:shadow-md transition-shadow"
            >
              <span className="text-2xl font-bold text-black">logo</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
