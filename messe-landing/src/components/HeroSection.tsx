type HeroSectionProps = {
  className?: string;
};

export default function HeroSection({ className = '' }: HeroSectionProps) {
  return (
    <section className={`bg-gradient-to-b from-gray-200 to-gray-400 py-20 ${className}`}>
      <div className="max-w-container mx-auto px-4 flex flex-col items-center justify-center min-h-[400px]">
        {/* Play Button */}
        <div className="mb-8">
          <div className="w-16 h-16 bg-white rounded-custom flex items-center justify-center shadow-lg cursor-pointer hover:scale-105 transition-transform">
            <svg className="w-8 h-8 text-black ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        </div>

        {/* CTA Button */}
        <button className="bg-primary text-white font-bold text-xl px-10 py-5 rounded-custom hover:bg-opacity-90 transition-all duration-300 shadow-lg">
          Discuss Your Project
        </button>
      </div>
    </section>
  );
}
