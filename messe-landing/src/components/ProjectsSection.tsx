type Project = {
  id: number;
  title: string;
  client: string;
  area: string;
  image: string;
};

import { Link } from 'react-router-dom';

type ProjectsSectionProps = {
  className?: string;
};

export default function ProjectsSection({ className = '' }: ProjectsSectionProps) {
  const projects: Project[] = [
    { id: 1, title: "Event", client: "Client", area: "m²", image: "" },
    { id: 2, title: "Event", client: "Client", area: "m²", image: "" },
    { id: 3, title: "Event", client: "Client", area: "m²", image: "" },
    { id: 4, title: "Event", client: "Client", area: "m²", image: "" },
  ];

  return (
    <section className={`py-16 bg-background ${className}`}>
      <div className="max-w-container mx-auto px-4">
        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
          {projects.map((project) => (
            <div key={project.id} className="flex flex-col">
              {/* Project Image */}
              <div className="bg-white rounded-custom h-48 mb-4 flex items-center justify-center">
                <div className="w-full h-full bg-gray-custom rounded-custom"></div>
              </div>
              
              {/* Project Info */}
              <div className="space-y-2">
                <div className="text-right">
                  <span className="text-2xl font-bold text-gray-text">{project.area}</span>
                </div>
                <div>
                  <p className="text-xl font-medium text-black">{project.client}</p>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-black">{project.title}</h3>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Statistics and CTA */}
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
          {/* Statistics */}
          <div className="text-center lg:text-left">
            <p className="text-xl font-bold text-[#656CAF]">
              150 Projects<br />
              Completed
            </p>
          </div>

          {/* All Projects Button */}
          <Link
            to="/projects"
            className="inline-block border-2 border-black text-black font-bold text-xl px-10 py-5 rounded-lg hover:bg-black hover:text-white transition-all duration-300"
          >
            All Projects
          </Link>
        </div>
      </div>
    </section>
  );
}
