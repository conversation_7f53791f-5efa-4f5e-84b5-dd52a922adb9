type HeaderProps = {
  className?: string;
};

export default function Header({ className = '' }: HeaderProps) {
  return (
    <header className={`bg-white py-4 ${className}`}>
      <div className="max-w-container mx-auto px-4 flex items-center justify-between">
        {/* Logo */}
        <div className="bg-background rounded-custom px-6 py-3">
          <span className="text-2xl font-bold text-black">logo</span>
        </div>

        {/* Navigation */}
        <nav className="hidden lg:flex items-center space-x-8">
          <a href="#projects" className="text-lg lg:text-2xl font-bold text-black hover:text-primary transition-colors">
            Projects
          </a>
          <a href="#about" className="text-lg lg:text-2xl font-bold text-black hover:text-primary transition-colors">
            About Us
          </a>
          <a href="tel:+97145485887" className="text-lg lg:text-2xl font-bold text-black hover:text-primary transition-colors">
            +971 4 548 5887
          </a>
        </nav>

        {/* Header Image */}
        <div className="w-12 h-12 bg-gray-custom rounded"></div>

        {/* Mobile menu button */}
        <button className="md:hidden p-2">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </header>
  );
}
