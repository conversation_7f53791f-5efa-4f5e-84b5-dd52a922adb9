import { Link, useLocation } from 'react-router-dom';

type HeaderProps = {
  className?: string;
};

export default function Header({ className = '' }: HeaderProps) {
  const location = useLocation();
  return (
    <header className={`bg-white py-4 ${className}`}>
      <div className="max-w-[1360px] mx-auto px-4 flex items-center justify-between">
        {/* Logo */}
        <div className="bg-[#E9E9E9] rounded-lg px-6 py-3">
          <span className="text-2xl font-bold text-black">logo</span>
        </div>

        {/* Navigation */}
        <nav className="hidden lg:flex items-center space-x-8">
          <Link
            to="/projects"
            className={`text-lg lg:text-2xl font-bold transition-colors ${
              location.pathname === '/projects' ? 'text-[#656CAF]' : 'text-black hover:text-[#656CAF]'
            }`}
          >
            Projects
          </Link>
          <Link
            to="/about"
            className={`text-lg lg:text-2xl font-bold transition-colors ${
              location.pathname === '/about' ? 'text-[#656CAF]' : 'text-black hover:text-[#656CAF]'
            }`}
          >
            About Us
          </Link>
          <a href="tel:+97145485887" className="text-lg lg:text-2xl font-bold text-black hover:text-[#656CAF] transition-colors">
            +971 4 548 5887
          </a>
        </nav>

        {/* Header Image */}
        <div className="w-12 h-12 bg-[#D9D9D9] rounded"></div>

        {/* Mobile menu button */}
        <button className="md:hidden p-2">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </header>
  );
}
