import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import App from '../App'

describe('App', () => {
  it('renders main sections', () => {
    render(<App />)
    
    // Check if main sections are rendered
    expect(screen.getByText('Our Projects & Clients')).toBeInTheDocument()
    expect(screen.getByText('Our Collaboration Process')).toBeInTheDocument()
    expect(screen.getByText('Why Choose Us?')).toBeInTheDocument()
    expect(screen.getByText("We're always happy to help")).toBeInTheDocument()
  })

  it('renders CTA buttons', () => {
    render(<App />)

    const discussButtons = screen.getAllByText('Discuss Your Project')
    expect(discussButtons.length).toBeGreaterThan(0)

    const allProjectsButtons = screen.getAllByText('All Projects')
    expect(allProjectsButtons.length).toBeGreaterThan(0)

    const knowUsButtons = screen.getAllByText('Get to Know Us Better')
    expect(knowUsButtons.length).toBeGreaterThan(0)
  })

  it('renders contact form', () => {
    render(<App />)
    
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/comments/i)).toBeInTheDocument()
  })
})
